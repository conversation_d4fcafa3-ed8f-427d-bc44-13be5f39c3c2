/* تحسينات التبويبات */

.tabs-container {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 0.5rem;
}

.tab-btn {
  background: none;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px 8px 0 0;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #6c757d;
  position: relative;
  min-width: 120px;
  justify-content: center;
}

.tab-btn:hover {
  background-color: #f8f9fa;
  color: #495057;
  transform: translateY(-2px);
}

.tab-btn.active {
  background-color: #2c5aa0;
  color: white;
  box-shadow: 0 4px 12px rgba(44, 90, 160, 0.3);
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 3px;
  background-color: #ff6b35;
  border-radius: 2px;
}

.tab-btn i {
  font-size: 1rem;
}

.tab-btn span {
  font-size: 0.9rem;
}

/* محتوى التبويبات */
.tab-content {
  animation: fadeInUp 0.4s ease-out;
  opacity: 1;
  transform: translateY(0);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
  .tabs-container {
    flex-wrap: wrap;
    gap: 0.25rem;
  }
  
  .tab-btn {
    padding: 0.5rem 1rem;
    min-width: auto;
    flex: 1;
    font-size: 0.85rem;
  }
  
  .tab-btn span {
    display: none;
  }
  
  .tab-btn i {
    font-size: 1.1rem;
  }
}

/* تحسين التركيز */
.tab-btn:focus {
  outline: 2px solid #ff6b35;
  outline-offset: 2px;
}

/* تحسين التبويبات في الوضع المظلم */
@media (prefers-color-scheme: dark) {
  .tab-btn {
    color: #adb5bd;
  }
  
  .tab-btn:hover {
    background-color: #343a40;
    color: #f8f9fa;
  }
  
  .tabs-container {
    border-bottom-color: #495057;
  }
}

/* تحسين الحالة المعطلة */
.tab-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* تحسين التحميل */
.tab-content.loading {
  opacity: 0.6;
  pointer-events: none;
}

.tab-content.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #2c5aa0;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* تحسين المحتوى الفارغ */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #6c757d;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h3 {
  margin-bottom: 0.5rem;
  color: #495057;
}

.empty-state p {
  margin-bottom: 1.5rem;
  opacity: 0.8;
}
