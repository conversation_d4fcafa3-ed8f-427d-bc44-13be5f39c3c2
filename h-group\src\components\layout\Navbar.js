const React = require('react');
const { useState } = React;
const { Link, NavLink, useNavigate, useLocation } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');
const { useSettings } = require('../../contexts/SettingsContext');
const { useNotifications } = require('../../contexts/NotificationsContext');
const NotificationsMenu = require('../notifications/NotificationsMenu');

const Navbar = () => {
  const { currentUser, logout } = useAuth();
  const { settings } = useSettings();
  const { unreadCount } = useNotifications();
  const navigate = useNavigate();
  const location = useLocation();
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showDropdowns, setShowDropdowns] = useState({
    production: false,
    hr: false,
    financial: false
  });

  // التعامل مع تسجيل الخروج
  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('خطأ في تسجيل الخروج:', error);
    }
  };

  // التبديل بين إظهار وإخفاء قائمة المستخدم
  const toggleUserMenu = () => {
    setShowUserMenu(!showUserMenu);
  };

  // التبديل بين إظهار وإخفاء القوائم المنسدلة
  const toggleDropdown = (dropdownName) => {
    setShowDropdowns(prev => ({
      ...prev,
      [dropdownName]: !prev[dropdownName]
    }));
  };

  // إغلاق جميع القوائم المنسدلة
  const closeAllDropdowns = () => {
    setShowDropdowns({
      production: false,
      hr: false,
      financial: false
    });
    setShowUserMenu(false);
  };

  // التحقق من النشاط للمجموعات
  const isActiveGroup = (paths) => {
    return paths.some(path => location.pathname.startsWith(path));
  };

  // التحقق من وجود المستخدم
  if (!currentUser) return null;

  return React.createElement('header', null,
    // الشريط العلوي الرئيسي
    React.createElement('nav', { className: 'navbar' },
      React.createElement('div', { className: 'navbar-container' },
        React.createElement(Link, {
          to: '/',
          className: 'navbar-brand'
        },
          React.createElement('i', { className: 'fas fa-industry' }),
          'H Group'
        ),
        React.createElement('div', { className: 'navbar-menu' },
          React.createElement(NotificationsMenu),
          React.createElement('div', { className: 'user-menu' },
            React.createElement('button', {
              className: 'navbar-item user-menu-button',
              onClick: toggleUserMenu
            },
              React.createElement('span', null, currentUser.full_name || currentUser.username),
              React.createElement('i', { className: 'fas fa-user' })
            ),
            showUserMenu && React.createElement('div', { className: 'user-menu-dropdown' },
              React.createElement(Link, { to: '/profile', className: 'user-menu-item' },
                React.createElement('i', { className: 'fas fa-user-circle' }),
                React.createElement('span', null, 'الملف الشخصي')
              ),
              currentUser.role === 'admin' && React.createElement(Link, { to: '/settings', className: 'user-menu-item' },
                React.createElement('i', { className: 'fas fa-cog' }),
                React.createElement('span', null, 'الإعدادات')
              ),
              React.createElement('button', {
                className: 'user-menu-item logout-button',
                onClick: handleLogout
              },
                React.createElement('i', { className: 'fas fa-sign-out-alt' }),
                React.createElement('span', null, 'تسجيل الخروج')
              )
            )
          )
        )
      )
    ),

    // شريط التنقل الرئيسي
    React.createElement('nav', { className: 'main-navigation' },
      React.createElement('div', { className: 'nav-container' },
        React.createElement('div', { className: 'nav-menu' },
          // لوحة التحكم
          React.createElement(NavLink, {
            to: '/',
            className: ({ isActive }) => isActive ? 'nav-item active' : 'nav-item',
            end: true
          },
            React.createElement('i', { className: 'fas fa-tachometer-alt' }),
            React.createElement('span', null, 'لوحة التحكم')
          ),

          // الطلبات المخصصة
          React.createElement(NavLink, {
            to: '/orders',
            className: ({ isActive }) => isActive ? 'nav-item active' : 'nav-item'
          },
            React.createElement('i', { className: 'fas fa-clipboard-list' }),
            React.createElement('span', null, 'الطلبات المخصصة')
          ),

          // العملاء
          React.createElement(NavLink, {
            to: '/customers',
            className: ({ isActive }) => isActive ? 'nav-item active' : 'nav-item'
          },
            React.createElement('i', { className: 'fas fa-users' }),
            React.createElement('span', null, 'العملاء')
          ),

          // إدارة الإنتاج مع قائمة منسدلة
          React.createElement('div', { className: 'nav-dropdown' },
            React.createElement('button', {
              className: `nav-item dropdown-toggle ${isActiveGroup(['/production', '/materials', '/inventory']) ? 'active' : ''}`,
              onClick: () => toggleDropdown('production')
            },
              React.createElement('i', { className: 'fas fa-industry' }),
              React.createElement('span', null, 'إدارة الإنتاج'),
              React.createElement('i', { className: `fas fa-chevron-down dropdown-arrow ${showDropdowns.production ? 'open' : ''}` })
            ),
            showDropdowns.production && React.createElement('div', { className: 'dropdown-menu' },
              React.createElement(NavLink, {
                to: '/production/materials',
                className: 'dropdown-item',
                onClick: closeAllDropdowns
              },
                React.createElement('i', { className: 'fas fa-boxes' }),
                React.createElement('span', null, 'المواد الخام')
              ),
              React.createElement(NavLink, {
                to: '/production/veneer',
                className: 'dropdown-item',
                onClick: closeAllDropdowns
              },
                React.createElement('i', { className: 'fas fa-layer-group' }),
                React.createElement('span', null, 'الفونير والقشرة')
              ),
              React.createElement(NavLink, {
                to: '/production/inventory',
                className: 'dropdown-item',
                onClick: closeAllDropdowns
              },
                React.createElement('i', { className: 'fas fa-warehouse' }),
                React.createElement('span', null, 'المخزون والجرد')
              ),
              React.createElement(NavLink, {
                to: '/production/reservations',
                className: 'dropdown-item',
                onClick: closeAllDropdowns
              },
                React.createElement('i', { className: 'fas fa-bookmark' }),
                React.createElement('span', null, 'حجوزات المواد')
              )
            )
          ),

          // الموارد البشرية مع قائمة منسدلة
          React.createElement('div', { className: 'nav-dropdown' },
            React.createElement('button', {
              className: `nav-item dropdown-toggle ${isActiveGroup(['/hr', '/workers', '/payroll']) ? 'active' : ''}`,
              onClick: () => toggleDropdown('hr')
            },
              React.createElement('i', { className: 'fas fa-users-cog' }),
              React.createElement('span', null, 'الموارد البشرية'),
              React.createElement('i', { className: `fas fa-chevron-down dropdown-arrow ${showDropdowns.hr ? 'open' : ''}` })
            ),
            showDropdowns.hr && React.createElement('div', { className: 'dropdown-menu' },
              React.createElement(NavLink, {
                to: '/hr/workers',
                className: 'dropdown-item',
                onClick: closeAllDropdowns
              },
                React.createElement('i', { className: 'fas fa-users' }),
                React.createElement('span', null, 'إدارة العمال')
              ),
              React.createElement(NavLink, {
                to: '/hr/payroll',
                className: 'dropdown-item',
                onClick: closeAllDropdowns
              },
                React.createElement('i', { className: 'fas fa-money-bill-wave' }),
                React.createElement('span', null, 'المرتبات والأجور')
              ),
              React.createElement(NavLink, {
                to: '/hr/advances',
                className: 'dropdown-item',
                onClick: closeAllDropdowns
              },
                React.createElement('i', { className: 'fas fa-hand-holding-usd' }),
                React.createElement('span', null, 'السلف والمقدمات')
              )
            )
          ),

          // الإدارة المالية مع قائمة منسدلة
          React.createElement('div', { className: 'nav-dropdown' },
            React.createElement('button', {
              className: `nav-item dropdown-toggle ${isActiveGroup(['/financial', '/invoices', '/expenses']) ? 'active' : ''}`,
              onClick: () => toggleDropdown('financial')
            },
              React.createElement('i', { className: 'fas fa-chart-line' }),
              React.createElement('span', null, 'الإدارة المالية'),
              React.createElement('i', { className: `fas fa-chevron-down dropdown-arrow ${showDropdowns.financial ? 'open' : ''}` })
            ),
            showDropdowns.financial && React.createElement('div', { className: 'dropdown-menu' },
              React.createElement(NavLink, {
                to: '/financial/invoices',
                className: 'dropdown-item',
                onClick: closeAllDropdowns
              },
                React.createElement('i', { className: 'fas fa-file-invoice-dollar' }),
                React.createElement('span', null, 'الفواتير والمدفوعات')
              ),
              React.createElement(NavLink, {
                to: '/financial/expenses',
                className: 'dropdown-item',
                onClick: closeAllDropdowns
              },
                React.createElement('i', { className: 'fas fa-industry' }),
                React.createElement('span', null, 'المصنع والتكاليف')
              ),
              React.createElement(NavLink, {
                to: '/financial/reports',
                className: 'dropdown-item',
                onClick: closeAllDropdowns
              },
                React.createElement('i', { className: 'fas fa-chart-bar' }),
                React.createElement('span', null, 'التقارير المالية')
              )
            )
          ),

          // التقارير
          React.createElement(NavLink, {
            to: '/reports',
            className: ({ isActive }) => isActive ? 'nav-item active' : 'nav-item'
          },
            React.createElement('i', { className: 'fas fa-chart-bar' }),
            React.createElement('span', null, 'التقارير')
          ),

          // الإعدادات (للمدير فقط)
          currentUser.role === 'admin' && React.createElement(NavLink, {
            to: '/settings',
            className: ({ isActive }) => isActive ? 'nav-item active' : 'nav-item'
          },
            React.createElement('i', { className: 'fas fa-cog' }),
            React.createElement('span', null, 'الإعدادات')
          )
        )
      )
    )
  );
};

module.exports = Navbar;
